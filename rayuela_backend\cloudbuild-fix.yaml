steps:
  # Build backend with dependency verification
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-f'
      - 'Dockerfile.fixed'
      - '-t'
      - 'gcr.io/$PROJECT_ID/rayuela-backend-1002953244539:$COMMIT_SHA'
      - '.'
    timeout: '1200s'

  # Push image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/rayuela-backend-1002953244539:$COMMIT_SHA'
    timeout: '600s'

images:
  - 'gcr.io/$PROJECT_ID/rayuela-backend-1002953244539:$COMMIT_SHA'

options:
  machineType: 'E2_HIGHCPU_8'
  timeout: '2000s'
