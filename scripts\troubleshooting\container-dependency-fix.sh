#!/bin/bash

echo "=== RAY<PERSON>LA BACKEND DEPENDENCY FIX - FINAL WORKING VERSION ==="

PROJECT_ID="lateral-insight-461112-g9"
SERVICE_NAME="rayuela-backend-1002953244539"
REGION="us-central1"

echo "🔧 Step 1: Creating working Dockerfile..."
cd rayuela_backend

# Backup original Dockerfile
cp Dockerfile Dockerfile.backup

# Create final working Dockerfile (dependencies verified in previous build)
cat > Dockerfile << 'EOF'
# Build stage
FROM python:3.12-slim AS builder

WORKDIR /app

# Install build dependencies including PostgreSQL dev libs
RUN apt-get update && apt-get install -y --no-install-recommends \
	build-essential \
	gcc \
	g++ \
	gfortran \
	libgomp1 \
	libopenblas-dev \
	libpq-dev \
	pkg-config \
	&& rm -rf /var/lib/apt/lists/*

# Copy requirements and add gunicorn
COPY requirements.txt .
RUN echo "gunicorn==21.2.0" >> requirements.txt

# Build wheels for all dependencies
RUN pip install --upgrade pip setuptools wheel
RUN pip wheel --no-cache-dir --no-deps --wheel-dir /app/wheels -r requirements.txt

# Final stage
FROM python:3.12-slim

# Install runtime dependencies including PostgreSQL client libs
RUN apt-get update && apt-get install -y --no-install-recommends \
	libgomp1 \
	libopenblas0 \
	libpq5 \
	curl \
	&& apt-get clean \
	&& rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -m -u 1000 appuser

WORKDIR /app

# Copy wheels and requirements from builder stage
COPY --from=builder /app/wheels /wheels
COPY --from=builder /app/requirements.txt .

# Install ALL dependencies from wheels
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir /wheels/* && \
    pip install --no-cache-dir asgiref==3.8.1 && \
    pip install --no-cache-dir itsdangerous && \
    pip install --no-cache-dir aiohttp && \
    rm -rf /wheels /root/.cache/pip/*

# Verify critical imports work during build
RUN python -c "import fastapi; import uvicorn; import sqlalchemy; import asyncpg; import pydantic; import dotenv; import google.cloud.storage; import redis; import celery; print('✅ All critical imports verified!')"

# Copy application code
COPY ./src ./src
COPY ./alembic ./alembic
COPY ./scripts ./scripts
COPY alembic.ini .
COPY main.py .
COPY gunicorn_conf.py .
COPY health_server.py .
COPY start.sh .

# Make scripts executable and change ownership
RUN chmod +x start.sh && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Environment variables for production
ENV ENV=production \
	PYTHONUNBUFFERED=1 \
	PYTHONDONTWRITEBYTECODE=1 \
	API_HOST=0.0.0.0 \
	PORT=8080

# Expose application port
EXPOSE 8080

# Health check
HEALTHCHECK CMD curl --fail http://localhost:8080/health || exit 1

# Use gunicorn with uvicorn workers for production
CMD ["gunicorn", "main:app", "-c", "gunicorn_conf.py"]
EOF

echo "✅ Working Dockerfile created (no extra test files needed)"

echo "🔧 Step 2: Building container image..."

# Generate immutable tag using timestamp and git commit
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
IMAGE_TAG="${TIMESTAMP}-${GIT_COMMIT}"

gcloud builds submit \
  --tag gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG} \
  --machine-type=e2-highcpu-8 \
  --timeout=1800s \
  --project=${PROJECT_ID} \
  .

if [ $? -eq 0 ]; then
    echo "✅ Container built successfully with tag: ${IMAGE_TAG}"

    echo "🔧 Step 3: Deploying to Cloud Run..."

    gcloud run deploy ${SERVICE_NAME} \
      --image=gcr.io/${PROJECT_ID}/${SERVICE_NAME}:${IMAGE_TAG} \
      --region=${REGION} \
      --platform=managed \
      --allow-unauthenticated \
      --service-account=rayuela-backend-sa@${PROJECT_ID}.iam.gserviceaccount.com \
      --memory=2Gi \
      --cpu=2 \
      --timeout=300 \
      --concurrency=80 \
      --min-instances=1 \
      --max-instances=10 \
      --set-env-vars="ENV=production,DEBUG=false,SKIP_SECRETS=true,SKIP_MIGRATIONS=true,LOG_LEVEL=INFO,GCP_PROJECT_ID=${PROJECT_ID},GCS_BUCKET_NAME=rayuela-production-bucket" \
      --vpc-connector=rayuela-vpc-connector \
      --vpc-egress=private-ranges-only \
      --project=${PROJECT_ID}

    if [ $? -eq 0 ]; then
        echo "✅ Service deployed successfully!"
        
        SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
          --region=${REGION} \
          --project=${PROJECT_ID} \
          --format='value(status.url)')
        
        echo "Service URL: ${SERVICE_URL}"
        echo "Waiting 30 seconds for service to be ready..."
        sleep 30
        
        echo "🧪 Testing the deployed service..."
        echo "Testing health endpoint: ${SERVICE_URL}/health"
        curl -s -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
          "${SERVICE_URL}/health"
        
        echo ""
        echo "Testing root endpoint: ${SERVICE_URL}/"
        curl -s -w "\nHTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
          "${SERVICE_URL}/"
          
        echo ""
        echo "🎉 DEPENDENCY FIX COMPLETED SUCCESSFULLY!"
        echo "✅ FastAPI dependencies are now working"
        echo "✅ Backend URL: ${SERVICE_URL}"
        
        # Restore original Dockerfile
        mv Dockerfile.backup Dockerfile
        echo "✅ Original Dockerfile restored"
        
    else
        echo "❌ Deployment failed"
        mv Dockerfile.backup Dockerfile
        exit 1
    fi
else
    echo "❌ Container build failed"
    gcloud builds list --limit=1 --project=${PROJECT_ID}
    mv Dockerfile.backup Dockerfile
    exit 1
fi 